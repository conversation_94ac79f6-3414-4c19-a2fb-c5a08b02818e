import { Bar, Chart } from 'react-chartjs-2';
import { generateBarData, barOptions, barOptionsAdmin } from 'utilities/graph-options';

const ReportBarGraph = ({ reportData, type, title, customerTitle, supplierTitle }) => {
  const generateSplitBarData = () => ({
    labels: reportData.map((data) => data.label || data.key_date.label),
    datasets: [
      {
        id: 1,
        label: 'Customer Spend',
        data: reportData.map((data) => data.customer_spend),
        backgroundColor: '#1f9e86',
        stack: 'customer-spend',
      },
      {
        id: 2,
        label: 'Supplier Cost',
        data: reportData.map((data) => data.supplier_cost),
        backgroundColor: '#dc2454',
        stack: 'supplier-cost',
      },
    ],
  });

  // Create dynamic options with date range title if provided
  const getChartOptions = (baseOptions) => {
    if (!title) return baseOptions;

    return {
      ...baseOptions,
      plugins: {
        ...baseOptions.plugins,
        title: {
          display: true,
          text: title,
          font: {
            family: 'Museo Slab',
            size: 16,
            weight: 'normal',
          },
          padding: {
            top: 10,
            bottom: 15,
          },
          color: '#666',
        },
      },
    };
  };

  return (
    <div className="reporting-graph">
      {type === 'CustomerProfile' && (
        <div className="between-flex">
          {customerTitle && (
            <h3 style={{ margin: 0, fontSize: '18px', fontFamily: 'Museo Slab', fontWeight: 'bold', color: '#333' }}>
              {customerTitle}
            </h3>
          )}
          <div className="between-flex" style={{ marginLeft: 'auto' }}>
            <span className="legend legend-catering">Catering</span>
            <span className="legend legend-snacks">Snacks</span>
          </div>
        </div>
      )}
      {type === 'SupplierProfile' && supplierTitle && (
        <div className="between-flex">
          <h3 style={{ margin: 0, fontSize: '18px', fontFamily: 'Museo Slab', fontWeight: 'bold', color: '#333' }}>
            {supplierTitle}
          </h3>
        </div>
      )}
      <div className="bar-container">
        {type === 'CustomerProfile' && <Chart options={getChartOptions(barOptions)} data={generateBarData(reportData)} />}
        {type === 'SupplierProfile' && <Bar options={getChartOptions(barOptionsAdmin)} data={generateSplitBarData(reportData)} />}
      </div>
    </div>
  );
};

export default ReportBarGraph;
