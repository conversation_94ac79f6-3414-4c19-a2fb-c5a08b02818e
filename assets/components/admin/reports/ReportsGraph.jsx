import { Bar, Chart } from 'react-chartjs-2';
import { generateBarData, barOptions, barOptionsAdmin } from 'utilities/graph-options';

const ReportBarGraph = ({ reportData, type, title }) => {
  const generateSplitBarData = () => ({
    labels: reportData.map((data) => data.label || data.key_date.label),
    datasets: [
      {
        id: 1,
        label: 'Customer Spend',
        data: reportData.map((data) => data.customer_spend),
        backgroundColor: '#1f9e86',
        stack: 'customer-spend',
      },
      {
        id: 2,
        label: 'Supplier Cost',
        data: reportData.map((data) => data.supplier_cost),
        backgroundColor: '#dc2454',
        stack: 'supplier-cost',
      },
    ],
  });

  // Create dynamic options with title if provided
  const getChartOptions = (baseOptions) => {
    if (!title) return baseOptions;

    return {
      ...baseOptions,
      plugins: {
        ...baseOptions.plugins,
        title: {
          display: true,
          text: title,
          font: {
            family: 'Museo Slab',
            size: 18,
            weight: 'bold',
          },
          padding: {
            top: 10,
            bottom: 20,
          },
          color: '#333',
        },
      },
    };
  };

  return (
    <div className="reporting-graph">
      {type === 'CustomerProfile' && (
        <div className="between-flex">
          <div className="between-flex" style={{ marginLeft: 'auto' }}>
            <span className="legend legend-catering">Catering</span>
            <span className="legend legend-snacks">Snacks</span>
          </div>
        </div>  
      )}      
      <div className="bar-container">
        {type === 'CustomerProfile' && <Chart options={getChartOptions(barOptions)} data={generateBarData(reportData)} />}
        {type === 'SupplierProfile' && <Bar options={getChartOptions(barOptionsAdmin)} data={generateSplitBarData(reportData)} />}
      </div>
    </div>
  );
};

export default ReportBarGraph;
